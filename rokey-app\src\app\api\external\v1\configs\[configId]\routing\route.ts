import { type NextRequest, NextResponse } from 'next/server';
import { ApiKeyAuthMiddleware } from '@/lib/userApiKeys/authMiddleware';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';
import { z } from 'zod';

// Use Edge Runtime for better performance
export const runtime = 'edge';

const authMiddleware = new ApiKeyAuthMiddleware();

interface RouteParams {
  params: Promise<{
    configId: string;
  }>;
}

// Validation schemas for routing configuration
const RoleAssignmentSchema = z.object({
  role_name: z.string().min(1),
  api_key_id: z.string().uuid(),
  priority: z.number().int().min(1).optional().default(1)
});

const AgentAssignmentSchema = z.object({
  agent_number: z.number().int().min(1).max(5),
  api_key_id: z.string().uuid(),
  agent_prompt: z.string().optional()
});

const RoutingConfigSchema = z.object({
  routing_strategy: z.enum(['load_balancing', 'role_routing', 'agent_mode']),
  settings: z.object({
    // Load balancing settings
    load_balancing_method: z.enum(['round_robin', 'random', 'least_used']).optional(),
    
    // Role routing settings
    role_assignments: z.array(RoleAssignmentSchema).optional(),
    enable_multi_role_orchestration: z.boolean().optional(),
    synthesis_model: z.string().optional(),
    
    // Agent mode settings
    agent_assignments: z.array(AgentAssignmentSchema).optional(),
    complexity_threshold: z.number().int().min(1).max(5).optional(),
    max_debate_rounds: z.number().int().min(1).max(10).optional().default(3),
    consensus_threshold: z.number().min(0.5).max(1).optional().default(0.6),
    
    // General settings
    fallback_strategy: z.enum(['first_available', 'random', 'none']).optional().default('first_available'),
    enable_caching: z.boolean().optional().default(true),
    cache_ttl_minutes: z.number().int().min(1).max(1440).optional().default(60),
    timeout_seconds: z.number().int().min(10).max(300).optional().default(60)
  }).optional().default({})
});

// GET /api/external/v1/configs/{configId}/routing - Get routing configuration
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // 1. Authenticate using user-generated API key
    const authResult = await authMiddleware.authenticateRequest(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        {
          error: {
            message: authResult.error,
            type: 'authentication_error',
            code: 'invalid_api_key'
          }
        },
        { status: authResult.statusCode || 401 }
      );
    }

    const { userApiKey, userConfig, ipAddress } = authResult;
    const { configId } = await params;

    // 2. Get configuration with routing settings
    const supabase = createSupabaseServerClientFromRequest(request);
    
    const { data: config, error } = await supabase
      .from('custom_api_configs')
      .select(`
        id,
        name,
        routing_strategy,
        settings,
        role_assignments(
          id,
          role_name,
          api_key_id,
          priority,
          api_keys(
            id,
            label,
            provider,
            status
          )
        ),
        agent_assignments(
          id,
          agent_number,
          api_key_id,
          agent_prompt,
          api_keys(
            id,
            label,
            provider,
            status
          )
        )
      `)
      .eq('id', configId)
      .eq('user_id', userConfig!.user_id)
      .single();

    if (error || !config) {
      return NextResponse.json(
        {
          error: {
            message: 'Configuration not found',
            type: 'not_found_error',
            code: 'config_not_found'
          }
        },
        { status: 404 }
      );
    }

    // 3. Log API usage
    authMiddleware.logApiUsage(
      userApiKey!,
      request,
      {
        statusCode: 200,
        modelUsed: 'routing_management',
        providerUsed: 'rokey_api',
      },
      ipAddress
    ).catch(console.error);

    return NextResponse.json({
      id: config.id,
      object: 'routing_config',
      name: config.name,
      routing_strategy: config.routing_strategy,
      settings: config.settings || {},
      role_assignments: config.role_assignments || [],
      agent_assignments: config.agent_assignments || []
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, PUT, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
      }
    });

  } catch (error) {
    console.error('Error in routing config GET API:', error);
    return NextResponse.json(
      {
        error: {
          message: 'Internal server error',
          type: 'server_error',
          code: 'internal_error'
        }
      },
      { status: 500 }
    );
  }
}

// PUT /api/external/v1/configs/{configId}/routing - Update routing configuration
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    // 1. Authenticate using user-generated API key
    const authResult = await authMiddleware.authenticateRequest(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        {
          error: {
            message: authResult.error,
            type: 'authentication_error',
            code: 'invalid_api_key'
          }
        },
        { status: authResult.statusCode || 401 }
      );
    }

    const { userApiKey, userConfig, ipAddress } = authResult;
    const { configId } = await params;

    // 2. Validate request body
    const body = await request.json();
    const validationResult = RoutingConfigSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: {
            message: 'Invalid routing configuration',
            type: 'validation_error',
            code: 'invalid_parameters',
            details: validationResult.error.errors
          }
        },
        { status: 400 }
      );
    }

    const routingConfig = validationResult.data;

    // 3. Verify config belongs to user
    const supabase = createSupabaseServerClientFromRequest(request);
    
    const { data: config, error: configError } = await supabase
      .from('custom_api_configs')
      .select('id, user_id')
      .eq('id', configId)
      .eq('user_id', userConfig!.user_id)
      .single();

    if (configError || !config) {
      return NextResponse.json(
        {
          error: {
            message: 'Configuration not found',
            type: 'not_found_error',
            code: 'config_not_found'
          }
        },
        { status: 404 }
      );
    }

    // 4. Update routing configuration
    const { data: updatedConfig, error: updateError } = await supabase
      .from('custom_api_configs')
      .update({
        routing_strategy: routingConfig.routing_strategy,
        settings: routingConfig.settings,
        updated_at: new Date().toISOString()
      })
      .eq('id', configId)
      .eq('user_id', userConfig!.user_id)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating routing config:', updateError);
      return NextResponse.json(
        {
          error: {
            message: 'Failed to update routing configuration',
            type: 'server_error',
            code: 'database_error'
          }
        },
        { status: 500 }
      );
    }

    // 5. Update role assignments if provided
    if (routingConfig.settings.role_assignments) {
      // Delete existing role assignments
      await supabase
        .from('role_assignments')
        .delete()
        .eq('custom_api_config_id', configId);

      // Insert new role assignments
      if (routingConfig.settings.role_assignments.length > 0) {
        const roleAssignments = routingConfig.settings.role_assignments.map(assignment => ({
          custom_api_config_id: configId,
          role_name: assignment.role_name,
          api_key_id: assignment.api_key_id,
          priority: assignment.priority
        }));

        await supabase
          .from('role_assignments')
          .insert(roleAssignments);
      }
    }

    // 6. Update agent assignments if provided
    if (routingConfig.settings.agent_assignments) {
      // Delete existing agent assignments
      await supabase
        .from('agent_assignments')
        .delete()
        .eq('custom_api_config_id', configId);

      // Insert new agent assignments
      if (routingConfig.settings.agent_assignments.length > 0) {
        const agentAssignments = routingConfig.settings.agent_assignments.map(assignment => ({
          custom_api_config_id: configId,
          agent_number: assignment.agent_number,
          api_key_id: assignment.api_key_id,
          agent_prompt: assignment.agent_prompt
        }));

        await supabase
          .from('agent_assignments')
          .insert(agentAssignments);
      }
    }

    // 7. Log API usage
    authMiddleware.logApiUsage(
      userApiKey!,
      request,
      {
        statusCode: 200,
        modelUsed: 'routing_management',
        providerUsed: 'rokey_api',
      },
      ipAddress
    ).catch(console.error);

    return NextResponse.json({
      id: updatedConfig.id,
      object: 'routing_config',
      name: updatedConfig.name,
      routing_strategy: updatedConfig.routing_strategy,
      settings: updatedConfig.settings || {},
      updated_at: updatedConfig.updated_at,
      message: 'Routing configuration updated successfully'
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, PUT, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
      }
    });

  } catch (error) {
    console.error('Error in routing config PUT API:', error);
    return NextResponse.json(
      {
        error: {
          message: 'Internal server error',
          type: 'server_error',
          code: 'internal_error'
        }
      },
      { status: 500 }
    );
  }
}

// OPTIONS handler for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, PUT, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    },
  });
}
