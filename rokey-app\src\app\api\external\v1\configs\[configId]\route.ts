import { type NextRequest, NextResponse } from 'next/server';
import { ApiKeyAuthMiddleware } from '@/lib/userApiKeys/authMiddleware';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';
import { z } from 'zod';

// Use Edge Runtime for better performance
export const runtime = 'edge';

const authMiddleware = new ApiKeyAuthMiddleware();

interface RouteParams {
  params: Promise<{
    configId: string;
  }>;
}

// Validation schema
const UpdateConfigSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.string().max(500).optional(),
  routing_strategy: z.enum(['load_balancing', 'role_routing', 'agent_mode']).optional(),
  settings: z.object({
    temperature: z.number().min(0).max(2).optional(),
    max_tokens: z.number().int().positive().optional(),
    top_p: z.number().min(0).max(1).optional(),
    frequency_penalty: z.number().min(-2).max(2).optional(),
    presence_penalty: z.number().min(-2).max(2).optional(),
  }).optional()
});

// GET /api/external/v1/configs/{configId} - Get specific configuration
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // 1. Authenticate using user-generated API key
    const authResult = await authMiddleware.authenticateRequest(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        {
          error: {
            message: authResult.error,
            type: 'authentication_error',
            code: 'invalid_api_key'
          }
        },
        { status: authResult.statusCode || 401 }
      );
    }

    const { userApiKey, userConfig, ipAddress } = authResult;
    const { configId } = await params;

    // 2. Get configuration
    const supabase = createSupabaseServerClientFromRequest(request);
    
    const { data: config, error } = await supabase
      .from('custom_api_configs')
      .select(`
        id,
        name,
        description,
        routing_strategy,
        settings,
        created_at,
        updated_at,
        api_keys(
          id,
          provider,
          label,
          status,
          is_default_general_chat_model,
          temperature,
          predefined_models(name, display_name)
        )
      `)
      .eq('id', configId)
      .eq('user_id', userConfig!.user_id)
      .single();

    if (error || !config) {
      return NextResponse.json(
        {
          error: {
            message: 'Configuration not found',
            type: 'not_found_error',
            code: 'config_not_found'
          }
        },
        { status: 404 }
      );
    }

    // 3. Log API usage
    authMiddleware.logApiUsage(
      userApiKey!,
      request,
      {
        statusCode: 200,
        modelUsed: 'config_management',
        providerUsed: 'rokey_api',
      },
      ipAddress
    ).catch(console.error);

    return NextResponse.json({
      id: config.id,
      object: 'config',
      name: config.name,
      description: config.description,
      routing_strategy: config.routing_strategy,
      settings: config.settings,
      created_at: config.created_at,
      updated_at: config.updated_at,
      api_keys: config.api_keys || []
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
      }
    });

  } catch (error) {
    console.error('Error in config GET API:', error);
    return NextResponse.json(
      {
        error: {
          message: 'Internal server error',
          type: 'server_error',
          code: 'internal_error'
        }
      },
      { status: 500 }
    );
  }
}

// PUT /api/external/v1/configs/{configId} - Update configuration
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    // 1. Authenticate using user-generated API key
    const authResult = await authMiddleware.authenticateRequest(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        {
          error: {
            message: authResult.error,
            type: 'authentication_error',
            code: 'invalid_api_key'
          }
        },
        { status: authResult.statusCode || 401 }
      );
    }

    const { userApiKey, userConfig, ipAddress } = authResult;
    const { configId } = await params;

    // 2. Validate request body
    const body = await request.json();
    const validationResult = UpdateConfigSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: {
            message: 'Invalid request data',
            type: 'validation_error',
            code: 'invalid_parameters',
            details: validationResult.error.errors
          }
        },
        { status: 400 }
      );
    }

    const updateData = validationResult.data;

    // 3. Update configuration
    const supabase = createSupabaseServerClientFromRequest(request);
    
    const { data: updatedConfig, error } = await supabase
      .from('custom_api_configs')
      .update({
        ...updateData,
        updated_at: new Date().toISOString()
      })
      .eq('id', configId)
      .eq('user_id', userConfig!.user_id)
      .select()
      .single();

    if (error || !updatedConfig) {
      return NextResponse.json(
        {
          error: {
            message: 'Configuration not found or failed to update',
            type: 'not_found_error',
            code: 'config_not_found'
          }
        },
        { status: 404 }
      );
    }

    // 4. Log API usage
    authMiddleware.logApiUsage(
      userApiKey!,
      request,
      {
        statusCode: 200,
        modelUsed: 'config_management',
        providerUsed: 'rokey_api',
      },
      ipAddress
    ).catch(console.error);

    return NextResponse.json({
      id: updatedConfig.id,
      object: 'config',
      name: updatedConfig.name,
      description: updatedConfig.description,
      routing_strategy: updatedConfig.routing_strategy,
      settings: updatedConfig.settings,
      created_at: updatedConfig.created_at,
      updated_at: updatedConfig.updated_at
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
      }
    });

  } catch (error) {
    console.error('Error in config PUT API:', error);
    return NextResponse.json(
      {
        error: {
          message: 'Internal server error',
          type: 'server_error',
          code: 'internal_error'
        }
      },
      { status: 500 }
    );
  }
}

// DELETE /api/external/v1/configs/{configId} - Delete configuration
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    // 1. Authenticate using user-generated API key
    const authResult = await authMiddleware.authenticateRequest(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        {
          error: {
            message: authResult.error,
            type: 'authentication_error',
            code: 'invalid_api_key'
          }
        },
        { status: authResult.statusCode || 401 }
      );
    }

    const { userApiKey, userConfig, ipAddress } = authResult;
    const { configId } = await params;

    // 2. Check if config exists and belongs to user
    const supabase = createSupabaseServerClientFromRequest(request);
    
    const { data: config, error: fetchError } = await supabase
      .from('custom_api_configs')
      .select('id, name')
      .eq('id', configId)
      .eq('user_id', userConfig!.user_id)
      .single();

    if (fetchError || !config) {
      return NextResponse.json(
        {
          error: {
            message: 'Configuration not found',
            type: 'not_found_error',
            code: 'config_not_found'
          }
        },
        { status: 404 }
      );
    }

    // 3. Delete configuration (this will cascade delete related API keys)
    const { error: deleteError } = await supabase
      .from('custom_api_configs')
      .delete()
      .eq('id', configId)
      .eq('user_id', userConfig!.user_id);

    if (deleteError) {
      console.error('Error deleting config:', deleteError);
      return NextResponse.json(
        {
          error: {
            message: 'Failed to delete configuration',
            type: 'server_error',
            code: 'database_error'
          }
        },
        { status: 500 }
      );
    }

    // 4. Log API usage
    authMiddleware.logApiUsage(
      userApiKey!,
      request,
      {
        statusCode: 200,
        modelUsed: 'config_management',
        providerUsed: 'rokey_api',
      },
      ipAddress
    ).catch(console.error);

    return NextResponse.json({
      id: configId,
      object: 'config',
      deleted: true
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
      }
    });

  } catch (error) {
    console.error('Error in config DELETE API:', error);
    return NextResponse.json(
      {
        error: {
          message: 'Internal server error',
          type: 'server_error',
          code: 'internal_error'
        }
      },
      { status: 500 }
    );
  }
}

// OPTIONS handler for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    },
  });
}
