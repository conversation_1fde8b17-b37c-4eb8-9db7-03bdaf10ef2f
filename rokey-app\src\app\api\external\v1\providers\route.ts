import { type NextRequest, NextResponse } from 'next/server';
import { ApiKeyAuthMiddleware } from '@/lib/userApiKeys/authMiddleware';

// Use Edge Runtime for better performance
export const runtime = 'edge';

const authMiddleware = new ApiKeyAuthMiddleware();

// Static provider information
const PROVIDERS = [
  {
    id: 'openai',
    name: 'OpenAI',
    description: 'Leading AI research company with GPT models',
    website: 'https://openai.com',
    api_base_url: 'https://api.openai.com/v1',
    supported_features: ['chat', 'completions', 'embeddings', 'images', 'audio', 'vision'],
    authentication_type: 'bearer_token',
    rate_limits: {
      requests_per_minute: 3500,
      tokens_per_minute: 90000
    }
  },
  {
    id: 'anthropic',
    name: 'Anthropic',
    description: 'AI safety company with Claude models',
    website: 'https://anthropic.com',
    api_base_url: 'https://api.anthropic.com/v1',
    supported_features: ['chat', 'completions', 'vision'],
    authentication_type: 'api_key',
    rate_limits: {
      requests_per_minute: 1000,
      tokens_per_minute: 40000
    }
  },
  {
    id: 'google',
    name: 'Google AI',
    description: 'Google\'s Gemini models and AI services',
    website: 'https://ai.google.dev',
    api_base_url: 'https://generativelanguage.googleapis.com/v1beta',
    supported_features: ['chat', 'completions', 'vision', 'embeddings'],
    authentication_type: 'api_key',
    rate_limits: {
      requests_per_minute: 1500,
      tokens_per_minute: 32000
    }
  },
  {
    id: 'cohere',
    name: 'Cohere',
    description: 'Enterprise-focused language models',
    website: 'https://cohere.ai',
    api_base_url: 'https://api.cohere.ai/v1',
    supported_features: ['chat', 'completions', 'embeddings', 'rerank'],
    authentication_type: 'bearer_token',
    rate_limits: {
      requests_per_minute: 1000,
      tokens_per_minute: 40000
    }
  },
  {
    id: 'mistral',
    name: 'Mistral AI',
    description: 'Open and efficient language models',
    website: 'https://mistral.ai',
    api_base_url: 'https://api.mistral.ai/v1',
    supported_features: ['chat', 'completions', 'embeddings'],
    authentication_type: 'bearer_token',
    rate_limits: {
      requests_per_minute: 1000,
      tokens_per_minute: 30000
    }
  },
  {
    id: 'perplexity',
    name: 'Perplexity AI',
    description: 'Search-augmented language models',
    website: 'https://perplexity.ai',
    api_base_url: 'https://api.perplexity.ai',
    supported_features: ['chat', 'completions', 'search'],
    authentication_type: 'bearer_token',
    rate_limits: {
      requests_per_minute: 500,
      tokens_per_minute: 20000
    }
  },
  {
    id: 'groq',
    name: 'Groq',
    description: 'Ultra-fast inference for language models',
    website: 'https://groq.com',
    api_base_url: 'https://api.groq.com/openai/v1',
    supported_features: ['chat', 'completions'],
    authentication_type: 'bearer_token',
    rate_limits: {
      requests_per_minute: 30,
      tokens_per_minute: 6000
    }
  },
  {
    id: 'together',
    name: 'Together AI',
    description: 'Open source models with fast inference',
    website: 'https://together.ai',
    api_base_url: 'https://api.together.xyz/v1',
    supported_features: ['chat', 'completions', 'embeddings'],
    authentication_type: 'bearer_token',
    rate_limits: {
      requests_per_minute: 600,
      tokens_per_minute: 30000
    }
  },
  {
    id: 'fireworks',
    name: 'Fireworks AI',
    description: 'Fast inference for open source models',
    website: 'https://fireworks.ai',
    api_base_url: 'https://api.fireworks.ai/inference/v1',
    supported_features: ['chat', 'completions', 'embeddings'],
    authentication_type: 'bearer_token',
    rate_limits: {
      requests_per_minute: 600,
      tokens_per_minute: 40000
    }
  },
  {
    id: 'deepseek',
    name: 'DeepSeek',
    description: 'Advanced reasoning and coding models',
    website: 'https://deepseek.com',
    api_base_url: 'https://api.deepseek.com/v1',
    supported_features: ['chat', 'completions'],
    authentication_type: 'bearer_token',
    rate_limits: {
      requests_per_minute: 500,
      tokens_per_minute: 30000
    }
  }
];

// GET /api/external/v1/providers - List all supported providers
export async function GET(request: NextRequest) {
  try {
    // 1. Authenticate using user-generated API key
    const authResult = await authMiddleware.authenticateRequest(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        {
          error: {
            message: authResult.error,
            type: 'authentication_error',
            code: 'invalid_api_key'
          }
        },
        { status: authResult.statusCode || 401 }
      );
    }

    const { userApiKey, userConfig, ipAddress } = authResult;

    // 2. Get query parameters for filtering
    const { searchParams } = new URL(request.url);
    const feature = searchParams.get('feature'); // Filter by supported feature
    const authType = searchParams.get('auth_type'); // Filter by authentication type

    // 3. Filter providers based on query parameters
    let filteredProviders = PROVIDERS;

    if (feature) {
      filteredProviders = filteredProviders.filter(provider => 
        provider.supported_features.includes(feature)
      );
    }

    if (authType) {
      filteredProviders = filteredProviders.filter(provider => 
        provider.authentication_type === authType
      );
    }

    // 4. Log API usage
    authMiddleware.logApiUsage(
      userApiKey!,
      request,
      {
        statusCode: 200,
        modelUsed: 'provider_listing',
        providerUsed: 'rokey_api',
      },
      ipAddress
    ).catch(console.error);

    return NextResponse.json({
      object: 'list',
      data: filteredProviders,
      has_more: false,
      total_count: filteredProviders.length
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
        'X-RouKey-Total-Providers': PROVIDERS.length.toString(),
        'X-RouKey-Filtered-Count': filteredProviders.length.toString(),
      }
    });

  } catch (error) {
    console.error('Error in providers GET API:', error);
    return NextResponse.json(
      {
        error: {
          message: 'Internal server error',
          type: 'server_error',
          code: 'internal_error'
        }
      },
      { status: 500 }
    );
  }
}

// OPTIONS handler for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    },
  });
}
