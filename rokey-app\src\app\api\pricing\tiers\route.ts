import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Return pricing tiers data
    const pricingTiers = [
      {
        id: 'free',
        name: 'Free',
        price: 0,
        currency: 'USD',
        interval: 'month',
        features: [
          'Unlimited API requests',
          '1 Custom Configuration',
          '3 API Keys per config',
          'All 300+ AI models',
          'Strict fallback routing only',
          'Basic analytics only',
          'Community support'
        ],
        popular: false
      },
      {
        id: 'starter',
        name: 'Starter',
        price: 19,
        currency: 'USD',
        interval: 'month',
        features: [
          'Unlimited API requests',
          '15 Custom Configurations',
          '5 API Keys per config',
          'All 300+ AI models',
          'Intelligent routing strategies',
          'Up to 3 custom roles',
          'Enhanced logs and analytics',
          'Community support'
        ],
        popular: false
      },
      {
        id: 'professional',
        name: 'Professional',
        price: 49,
        currency: 'USD',
        interval: 'month',
        features: [
          'Unlimited API requests',
          'Unlimited Custom Configurations',
          'Unlimited API Keys per config',
          'All 300+ AI models',
          'All advanced routing strategies',
          'Unlimited custom roles',
          'Knowledge base (5 documents)',
          'Semantic caching',
          'Advanced analytics and logging',
          'Priority email support'
        ],
        popular: true
      }
    ];

    return NextResponse.json({
      success: true,
      data: pricingTiers
    });
  } catch (error) {
    console.error('Error fetching pricing tiers:', error);
    return NextResponse.json(
      { error: 'Failed to fetch pricing tiers' },
      { status: 500 }
    );
  }
}
