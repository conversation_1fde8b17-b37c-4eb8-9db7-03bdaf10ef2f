'use client';

import { useState, useEffect } from 'react';

interface MobileResponsiveState {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isLargeDesktop: boolean;
  isTouchDevice: boolean;
  orientation: 'portrait' | 'landscape';
  width: number;
  height: number;
  safeAreaInsets: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
}

export const useMobileResponsive = (): MobileResponsiveState => {
  const [state, setState] = useState<MobileResponsiveState>({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    isLargeDesktop: true,
    isTouchDevice: false,
    orientation: 'landscape',
    width: 1920,
    height: 1080,
    safeAreaInsets: {
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
    },
  });

  useEffect(() => {
    const updateState = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      // Detect touch device
      const isTouchDevice = 'ontouchstart' in window || 
        navigator.maxTouchPoints > 0 || 
        (navigator as any).msMaxTouchPoints > 0;
      
      // Determine orientation
      const orientation = width > height ? 'landscape' : 'portrait';
      
      // Get safe area insets (for iOS devices with notches)
      const computedStyle = getComputedStyle(document.documentElement);
      const safeAreaInsets = {
        top: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-top)') || '0'),
        bottom: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-bottom)') || '0'),
        left: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-left)') || '0'),
        right: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-right)') || '0'),
      };
      
      setState({
        isMobile: width < 768,
        isTablet: width >= 768 && width < 1024,
        isDesktop: width >= 1024,
        isLargeDesktop: width >= 1280,
        isTouchDevice,
        orientation,
        width,
        height,
        safeAreaInsets,
      });
    };

    // Initial check
    updateState();

    // Add event listeners
    window.addEventListener('resize', updateState);
    window.addEventListener('orientationchange', updateState);

    // Cleanup
    return () => {
      window.removeEventListener('resize', updateState);
      window.removeEventListener('orientationchange', updateState);
    };
  }, []);

  return state;
};

// Utility functions for responsive design
export const getResponsiveValue = <T>(
  mobile: T,
  tablet: T,
  desktop: T,
  largeDesktop?: T
) => {
  const { isMobile, isTablet, isDesktop, isLargeDesktop } = useMobileResponsive();
  
  if (isMobile) return mobile;
  if (isTablet) return tablet;
  if (isLargeDesktop && largeDesktop !== undefined) return largeDesktop;
  if (isDesktop) return desktop;
  
  return desktop; // fallback
};

export const getResponsiveClasses = (
  mobileClasses: string,
  tabletClasses?: string,
  desktopClasses?: string,
  largeDesktopClasses?: string
) => {
  const { isMobile, isTablet, isDesktop, isLargeDesktop } = useMobileResponsive();
  
  if (isMobile) return mobileClasses;
  if (isTablet && tabletClasses) return tabletClasses;
  if (isLargeDesktop && largeDesktopClasses) return largeDesktopClasses;
  if (isDesktop && desktopClasses) return desktopClasses;
  
  return mobileClasses; // fallback to mobile-first
};

// Hook for managing mobile sidebar states
export const useMobileSidebar = () => {
  const [isMainSidebarOpen, setIsMainSidebarOpen] = useState(false);
  const [isHistorySidebarOpen, setIsHistorySidebarOpen] = useState(false);
  const { isMobile } = useMobileResponsive();

  const toggleMainSidebar = () => {
    setIsMainSidebarOpen(!isMainSidebarOpen);
    // Close history sidebar when opening main sidebar on mobile
    if (isMobile && !isMainSidebarOpen) {
      setIsHistorySidebarOpen(false);
    }
  };

  const toggleHistorySidebar = () => {
    setIsHistorySidebarOpen(!isHistorySidebarOpen);
    // Close main sidebar when opening history sidebar on mobile
    if (isMobile && !isHistorySidebarOpen) {
      setIsMainSidebarOpen(false);
    }
  };

  const closeAllSidebars = () => {
    setIsMainSidebarOpen(false);
    setIsHistorySidebarOpen(false);
  };

  // Auto-close sidebars when switching from mobile to desktop
  useEffect(() => {
    if (!isMobile) {
      setIsMainSidebarOpen(false);
      setIsHistorySidebarOpen(false);
    }
  }, [isMobile]);

  return {
    isMainSidebarOpen,
    isHistorySidebarOpen,
    toggleMainSidebar,
    toggleHistorySidebar,
    closeAllSidebars,
    setIsMainSidebarOpen,
    setIsHistorySidebarOpen,
  };
};

// Hook for mobile-friendly touch interactions
export const useTouchInteractions = () => {
  const { isTouchDevice } = useMobileResponsive();
  const [isPressed, setIsPressed] = useState(false);

  const touchHandlers = {
    onTouchStart: () => setIsPressed(true),
    onTouchEnd: () => setIsPressed(false),
    onTouchCancel: () => setIsPressed(false),
  };

  const mouseHandlers = {
    onMouseDown: () => setIsPressed(true),
    onMouseUp: () => setIsPressed(false),
    onMouseLeave: () => setIsPressed(false),
  };

  return {
    isTouchDevice,
    isPressed,
    handlers: isTouchDevice ? touchHandlers : mouseHandlers,
  };
};

// Hook for managing mobile keyboard visibility
export const useMobileKeyboard = () => {
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  const { isMobile } = useMobileResponsive();

  useEffect(() => {
    if (!isMobile) return;

    const handleResize = () => {
      // On mobile, if the viewport height decreases significantly, 
      // it's likely the keyboard is visible
      const viewportHeight = window.visualViewport?.height || window.innerHeight;
      const screenHeight = window.screen.height;
      const keyboardThreshold = screenHeight * 0.75;
      
      setIsKeyboardVisible(viewportHeight < keyboardThreshold);
    };

    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', handleResize);
      return () => window.visualViewport?.removeEventListener('resize', handleResize);
    } else {
      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }
  }, [isMobile]);

  return { isKeyboardVisible };
};

// Utility for mobile-safe scrolling
export const useMobileScroll = () => {
  const { isMobile } = useMobileResponsive();

  const scrollToTop = (smooth = true) => {
    window.scrollTo({
      top: 0,
      behavior: smooth ? 'smooth' : 'auto',
    });
  };

  const scrollToElement = (elementId: string, offset = 0) => {
    const element = document.getElementById(elementId);
    if (element) {
      const top = element.offsetTop - offset;
      window.scrollTo({
        top,
        behavior: 'smooth',
      });
    }
  };

  const preventBodyScroll = (prevent: boolean) => {
    if (isMobile) {
      document.body.style.overflow = prevent ? 'hidden' : '';
    }
  };

  return {
    scrollToTop,
    scrollToElement,
    preventBodyScroll,
  };
};
