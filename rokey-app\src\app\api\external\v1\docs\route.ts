import { type NextRequest, NextResponse } from 'next/server';

// Use Edge Runtime for better performance
export const runtime = 'edge';

// GET /api/external/v1/docs - API Documentation (OpenAPI/Swagger compatible)
export async function GET(request: NextRequest) {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://roukey.online';
  
  const openApiSpec = {
    openapi: '3.0.3',
    info: {
      title: 'RouKey External API',
      description: 'Comprehensive API for managing RouKey configurations, provider keys, and intelligent routing. Build powerful AI applications with maximum control and flexibility.',
      version: '1.0.0',
      contact: {
        name: 'Rou<PERSON>ey Support',
        email: '<EMAIL>',
        url: 'https://roukey.online'
      },
      license: {
        name: 'Commercial License',
        url: 'https://roukey.online/license'
      }
    },
    servers: [
      {
        url: `${baseUrl}/api/external/v1`,
        description: 'Production server'
      }
    ],
    security: [
      {
        ApiKeyAuth: []
      }
    ],
    components: {
      securitySchemes: {
        ApiKeyAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'API Key',
          description: 'Use your RouKey-generated API key as a Bearer token'
        }
      },
      schemas: {
        Error: {
          type: 'object',
          properties: {
            error: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                type: { type: 'string' },
                code: { type: 'string' },
                details: { type: 'array', items: { type: 'object' } }
              }
            }
          }
        },
        Config: {
          type: 'object',
          properties: {
            id: { type: 'string', format: 'uuid' },
            object: { type: 'string', enum: ['config'] },
            name: { type: 'string' },
            description: { type: 'string' },
            routing_strategy: { type: 'string', enum: ['load_balancing', 'role_routing', 'agent_mode'] },
            settings: { type: 'object' },
            created_at: { type: 'string', format: 'date-time' },
            updated_at: { type: 'string', format: 'date-time' }
          }
        },
        ProviderKey: {
          type: 'object',
          properties: {
            id: { type: 'string', format: 'uuid' },
            object: { type: 'string', enum: ['provider_key'] },
            provider: { type: 'string' },
            label: { type: 'string' },
            status: { type: 'string', enum: ['active', 'inactive'] },
            is_default_general_chat_model: { type: 'boolean' },
            temperature: { type: 'number', minimum: 0, maximum: 2 },
            created_at: { type: 'string', format: 'date-time' },
            updated_at: { type: 'string', format: 'date-time' }
          }
        },
        Model: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            object: { type: 'string', enum: ['model'] },
            created: { type: 'integer' },
            owned_by: { type: 'string' },
            rokey_extensions: {
              type: 'object',
              properties: {
                display_name: { type: 'string' },
                description: { type: 'string' },
                family: { type: 'string' },
                context_window: { type: 'integer' },
                modality: { type: 'string' },
                provider: { type: 'string' }
              }
            }
          }
        }
      }
    },
    paths: {
      '/chat/completions': {
        post: {
          summary: 'Create chat completion',
          description: 'OpenAI-compatible chat completions with intelligent routing',
          tags: ['Chat'],
          requestBody: {
            required: true,
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  required: ['messages'],
                  properties: {
                    model: { type: 'string', default: 'gpt-3.5-turbo' },
                    messages: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          role: { type: 'string', enum: ['user', 'assistant', 'system'] },
                          content: { type: 'string' }
                        }
                      }
                    },
                    temperature: { type: 'number', minimum: 0, maximum: 2 },
                    max_tokens: { type: 'integer' },
                    stream: { type: 'boolean' },
                    role: { type: 'string', description: 'RouKey-specific role for intelligent routing' }
                  }
                }
              }
            }
          },
          responses: {
            '200': {
              description: 'Successful completion',
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      object: { type: 'string' },
                      created: { type: 'integer' },
                      model: { type: 'string' },
                      choices: { type: 'array' },
                      usage: { type: 'object' }
                    }
                  }
                }
              }
            }
          }
        }
      },
      '/configs': {
        get: {
          summary: 'List configurations',
          description: 'Get all your RouKey configurations',
          tags: ['Configuration Management'],
          responses: {
            '200': {
              description: 'List of configurations',
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                    properties: {
                      object: { type: 'string', enum: ['list'] },
                      data: {
                        type: 'array',
                        items: { $ref: '#/components/schemas/Config' }
                      }
                    }
                  }
                }
              }
            }
          }
        },
        post: {
          summary: 'Create configuration',
          description: 'Create a new RouKey configuration',
          tags: ['Configuration Management'],
          requestBody: {
            required: true,
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  required: ['name'],
                  properties: {
                    name: { type: 'string', maxLength: 100 },
                    description: { type: 'string', maxLength: 500 },
                    routing_strategy: { type: 'string', enum: ['load_balancing', 'role_routing', 'agent_mode'] },
                    settings: { type: 'object' }
                  }
                }
              }
            }
          },
          responses: {
            '201': {
              description: 'Configuration created',
              content: {
                'application/json': {
                  schema: { $ref: '#/components/schemas/Config' }
                }
              }
            }
          }
        }
      },
      '/configs/{configId}': {
        get: {
          summary: 'Get configuration',
          description: 'Get a specific configuration by ID',
          tags: ['Configuration Management'],
          parameters: [
            {
              name: 'configId',
              in: 'path',
              required: true,
              schema: { type: 'string', format: 'uuid' }
            }
          ],
          responses: {
            '200': {
              description: 'Configuration details',
              content: {
                'application/json': {
                  schema: { $ref: '#/components/schemas/Config' }
                }
              }
            }
          }
        },
        put: {
          summary: 'Update configuration',
          description: 'Update an existing configuration',
          tags: ['Configuration Management'],
          parameters: [
            {
              name: 'configId',
              in: 'path',
              required: true,
              schema: { type: 'string', format: 'uuid' }
            }
          ],
          requestBody: {
            required: true,
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    name: { type: 'string', maxLength: 100 },
                    description: { type: 'string', maxLength: 500 },
                    routing_strategy: { type: 'string', enum: ['load_balancing', 'role_routing', 'agent_mode'] },
                    settings: { type: 'object' }
                  }
                }
              }
            }
          },
          responses: {
            '200': {
              description: 'Configuration updated',
              content: {
                'application/json': {
                  schema: { $ref: '#/components/schemas/Config' }
                }
              }
            }
          }
        },
        delete: {
          summary: 'Delete configuration',
          description: 'Delete a configuration and all its associated keys',
          tags: ['Configuration Management'],
          parameters: [
            {
              name: 'configId',
              in: 'path',
              required: true,
              schema: { type: 'string', format: 'uuid' }
            }
          ],
          responses: {
            '200': {
              description: 'Configuration deleted',
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      object: { type: 'string' },
                      deleted: { type: 'boolean' }
                    }
                  }
                }
              }
            }
          }
        }
      },
      '/models': {
        get: {
          summary: 'List models',
          description: 'Get all available AI models (OpenAI-compatible)',
          tags: ['Models & Providers'],
          responses: {
            '200': {
              description: 'List of available models',
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                    properties: {
                      object: { type: 'string', enum: ['list'] },
                      data: {
                        type: 'array',
                        items: { $ref: '#/components/schemas/Model' }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      },
      '/usage': {
        get: {
          summary: 'Get usage analytics',
          description: 'Retrieve detailed usage statistics and analytics',
          tags: ['Analytics'],
          parameters: [
            {
              name: 'start_date',
              in: 'query',
              schema: { type: 'string', format: 'date-time' },
              description: 'Start date for usage period'
            },
            {
              name: 'end_date',
              in: 'query',
              schema: { type: 'string', format: 'date-time' },
              description: 'End date for usage period'
            },
            {
              name: 'granularity',
              in: 'query',
              schema: { type: 'string', enum: ['hour', 'day', 'week', 'month'] },
              description: 'Time granularity for aggregation'
            }
          ],
          responses: {
            '200': {
              description: 'Usage analytics data',
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                    properties: {
                      object: { type: 'string', enum: ['usage_report'] },
                      period: { type: 'object' },
                      totals: { type: 'object' },
                      time_series: { type: 'array' },
                      breakdowns: { type: 'object' }
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    tags: [
      {
        name: 'Chat',
        description: 'OpenAI-compatible chat completions with intelligent routing'
      },
      {
        name: 'Configuration Management',
        description: 'Manage your RouKey configurations'
      },
      {
        name: 'Provider Key Management',
        description: 'Manage API keys for different AI providers'
      },
      {
        name: 'Models & Providers',
        description: 'Information about available models and providers'
      },
      {
        name: 'Analytics',
        description: 'Usage statistics and analytics'
      },
      {
        name: 'Routing',
        description: 'Configure intelligent routing strategies'
      }
    ]
  };

  return NextResponse.json(openApiSpec, {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
      'Content-Type': 'application/json',
    }
  });
}

// OPTIONS handler for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    },
  });
}
