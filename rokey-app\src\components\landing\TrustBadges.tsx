'use client';

import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';

const useCountUp = (end: number, duration: number = 2000, delay: number = 0) => {
  const [count, setCount] = useState(0);
  const [hasStarted, setHasStarted] = useState(false);

  useEffect(() => {
    if (!hasStarted) return;

    const startTime = Date.now() + delay;
    const endTime = startTime + duration;

    const timer = setInterval(() => {
      const now = Date.now();
      if (now < startTime) return;

      const progress = Math.min((now - startTime) / duration, 1);
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      setCount(Math.floor(easeOutQuart * end));

      if (progress === 1) {
        clearInterval(timer);
      }
    }, 16);

    return () => clearInterval(timer);
  }, [end, duration, delay, hasStarted]);

  return { count, startCounting: () => setHasStarted(true) };
};

export default function TrustBadges() {
  const modelsCount = useCountUp(300, 2000, 200);
  const requestsCount = useCountUp(10, 2000, 400); // 10M
  const developersCount = useCountUp(5000, 2000, 600);
  const uptimeCount = useCountUp(99.9, 2000, 800);

  return (
    <section className="relative py-20 bg-gradient-to-r from-[#ff6b35] via-[#f7931e] to-[#ff6b35] overflow-hidden">
      {/* Subtle grid background */}
      <div
        className="absolute inset-0 opacity-10"
        style={{
          backgroundImage: `
            linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px'
        }}
      ></div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 15 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.3 }}
          onViewportEnter={() => {
            developersCount.startCounting();
            requestsCount.startCounting();
            uptimeCount.startCounting();
            modelsCount.startCounting();
          }}
          className="text-center mb-12"
        >
          <h3 className="text-white/90 text-2xl font-bold mb-3">
            Powering AI Innovation Worldwide
          </h3>
          <p className="text-white/70 text-lg mb-8">
            Join thousands of developers enjoying unlimited AI access with RouKey
          </p>

          {/* Product Hunt Badge */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.4, delay: 0.2 }}
            className="flex justify-center mb-8"
          >
            <a
              href="https://www.producthunt.com/products/roukey?embed=true&utm_source=badge-featured&utm_medium=badge&utm_source=badge-roukey"
              target="_blank"
              rel="noopener noreferrer"
              className="hover:scale-105 transition-transform duration-300 hover:drop-shadow-lg"
            >
              <img
                src="https://api.producthunt.com/widgets/embed-image/v1/featured.svg?post_id=993580&theme=dark&t=1752649260891"
                alt="RouKey - Route each task to the smartest AI for the job | Product Hunt"
                style={{ width: '250px', height: '54px' }}
                width="250"
                height="54"
                className="rounded-lg"
              />
            </a>
          </motion.div>
        </motion.div>

        {/* Animated Stats Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.1, duration: 0.3 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center"
        >
          <motion.div
            className="group"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <div className="text-4xl md:text-5xl font-bold text-white mb-3 group-hover:text-white/90 transition-colors duration-300">
              {modelsCount.count}+
            </div>
            <div className="text-white/80 text-base font-medium">
              AI Models Supported
            </div>
          </motion.div>

          <motion.div
            className="group"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <div className="text-4xl md:text-5xl font-bold text-white mb-3 group-hover:text-white/90 transition-colors duration-300">
              {requestsCount.count}M+
            </div>
            <div className="text-white/80 text-base font-medium">
              API Requests Processed
            </div>
          </motion.div>

          <motion.div
            className="group"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <div className="text-4xl md:text-5xl font-bold text-white mb-3 group-hover:text-white/90 transition-colors duration-300">
              {developersCount.count.toLocaleString()}+
            </div>
            <div className="text-white/80 text-base font-medium">
              Developers Trust Us
            </div>
          </motion.div>

          <motion.div
            className="group"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <div className="text-4xl md:text-5xl font-bold text-white mb-3 group-hover:text-white/90 transition-colors duration-300">
              {uptimeCount.count.toFixed(1)}%
            </div>
            <div className="text-white/80 text-base font-medium">
              Uptime Guarantee
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
