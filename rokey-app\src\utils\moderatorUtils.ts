// Enhanced moderator utilities for intelligent AI team coordination

import { OrchestrationEvent, OrchestrationEventType, emitOrchestrationEvent } from './orchestrationUtils';

export interface ModeratorDecision {
  action: 'proceed' | 'retry' | 'escalate' | 'modify' | 'synthesize';
  reasoning: string;
  modifications?: string;
  confidence: number;
  nextSteps?: string[];
}

export interface StepValidation {
  isValid: boolean;
  quality: number; // 0-1 score
  issues: string[];
  suggestions: string[];
  canProceed: boolean;
}

export interface SynthesisResult {
  combinedOutput: string;
  methodology: string;
  qualityScore: number;
  conflictsResolved: string[];
  improvements: string[];
}

// Enhanced moderator that acts as both classifier and orchestrator
export class EnhancedModerator {
  private classificationApiKey: string;
  private executionId: string;

  constructor(classificationApiKey: string, executionId: string) {
    this.classificationApiKey = classificationApiKey;
    this.executionId = executionId;
  }

  // Validate step output and decide next action
  async validateStepOutput(
    stepNumber: number,
    roleId: string,
    output: string,
    originalPrompt: string,
    expectedOutcome: string
  ): Promise<StepValidation> {
    const validationPrompt = `As an AI orchestration moderator, evaluate this step output:

Original Request: "${originalPrompt}"
Role: ${roleId}
Expected Outcome: ${expectedOutcome}
Actual Output: "${output}"

Evaluate:
1. Does the output fulfill the role's responsibility?
2. Is the quality sufficient for the next step?
3. Are there any issues or gaps?
4. Can we proceed to the next step?

Respond in JSON format:
{
  "isValid": true/false,
  "quality": 0.85,
  "issues": ["list of any issues"],
  "suggestions": ["list of improvements"],
  "canProceed": true/false,
  "reasoning": "detailed explanation"
}`;

    try {
      const response = await this.callModerator(validationPrompt);
      const validation = JSON.parse(response);
      
      // Emit validation event
      await emitOrchestrationEvent(
        this.executionId,
        'moderator_commentary',
        {
          commentary: `Validating ${roleId} output: ${validation.reasoning}`,
          validation: validation
        },
        stepNumber,
        roleId
      );

      return validation;
    } catch (error) {
      console.warn(`[Moderator] Validation error: ${error}`);
      
      // Fallback validation
      return {
        isValid: output.length > 50, // Basic length check
        quality: 0.7,
        issues: [],
        suggestions: [],
        canProceed: true
      };
    }
  }

  // Resolve conflicts between multiple model outputs
  async resolveConflicts(
    conflictingOutputs: Array<{
      roleId: string;
      output: string;
      confidence: number;
    }>,
    originalPrompt: string
  ): Promise<ModeratorDecision> {
    const conflictPrompt = `As an AI orchestration moderator, resolve conflicts between multiple AI outputs:

Original Request: "${originalPrompt}"

Conflicting Outputs:
${conflictingOutputs.map((output, index) => 
  `${index + 1}. ${output.roleId} (confidence: ${output.confidence}): "${output.output}"`
).join('\n')}

Determine the best approach:
1. Choose the best output
2. Combine elements from multiple outputs
3. Request modifications
4. Escalate for human review

Respond in JSON format:
{
  "action": "proceed|retry|escalate|modify|synthesize",
  "reasoning": "detailed explanation",
  "modifications": "specific changes needed (if action is modify)",
  "confidence": 0.85,
  "nextSteps": ["list of next actions"]
}`;

    try {
      const response = await this.callModerator(conflictPrompt);
      const decision = JSON.parse(response);
      
      await emitOrchestrationEvent(
        this.executionId,
        'moderator_commentary',
        {
          commentary: `Conflict resolution: ${decision.reasoning}`,
          decision: decision
        }
      );

      return decision;
    } catch (error) {
      console.warn(`[Moderator] Conflict resolution error: ${error}`);
      
      // Fallback: choose highest confidence output
      const bestOutput = conflictingOutputs.reduce((best, current) => 
        current.confidence > best.confidence ? current : best
      );

      return {
        action: 'proceed',
        reasoning: `Selected ${bestOutput.roleId} output with highest confidence (${bestOutput.confidence})`,
        confidence: 0.6,
        nextSteps: ['continue_with_selected_output']
      };
    }
  }

  // Synthesize multiple outputs into a cohesive final result
  async synthesizeOutputs(
    stepOutputs: Array<{
      stepNumber: number;
      roleId: string;
      output: string;
      quality: number;
    }>,
    originalPrompt: string
  ): Promise<SynthesisResult> {
    await emitOrchestrationEvent(
      this.executionId,
      'synthesis_started',
      {
        commentary: "🧩 Beginning synthesis of all specialist outputs...",
        totalSteps: stepOutputs.length
      }
    );

    const synthesisPrompt = `As an AI orchestration moderator, synthesize multiple specialist outputs into one cohesive response:

Original Request: "${originalPrompt}"

Specialist Outputs:
${stepOutputs.map(step =>
  `${step.stepNumber}. ${step.roleId} (quality: ${step.quality}): "${step.output}"`
).join('\n\n')}

CRITICAL SYNTHESIS REQUIREMENTS:
1. **PRESERVE ALL DETAILED CONTENT**: Include complete code scripts, full stories, detailed implementations, and all specific deliverables exactly as provided by specialists
2. **DO NOT SUMMARIZE OR CONDENSE**: If specialists provided full implementations, include them in their entirety in the combinedOutput
3. **MAINTAIN ORIGINAL FORMATTING**: Keep code blocks, markdown formatting, and structure intact
4. **INCLUDE ALL DELIVERABLES**: If specialists provided code, stories, translations, or other specific outputs, include them completely
5. **INTEGRATE LOGICALLY**: Structure the response clearly but ensure no detailed content is lost or abbreviated
6. **RESOLVE CONTRADICTIONS**: Address any conflicts while preserving all valuable detailed content
7. **ENSURE COMPLETENESS**: The combinedOutput should contain everything the user requested - full scripts, complete stories, detailed implementations

Your goal is to present a comprehensive response that includes ALL the detailed work from specialists, not summaries. Users want actual deliverables, not descriptions of them.

Respond in JSON format:
{
  "combinedOutput": "the complete synthesized response with all detailed content preserved",
  "methodology": "how you combined the outputs while preserving all details",
  "qualityScore": 0.92,
  "conflictsResolved": ["list of conflicts resolved"],
  "improvements": ["how the synthesis improved upon individual outputs while maintaining completeness"]
}`;

    try {
      // Stream synthesis progress
      await emitOrchestrationEvent(
        this.executionId,
        'synthesis_progress',
        {
          commentary: "🔄 Analyzing specialist contributions...",
          progress: 0.3
        }
      );

      const response = await this.callModerator(synthesisPrompt);
      
      await emitOrchestrationEvent(
        this.executionId,
        'synthesis_progress',
        {
          commentary: "🎨 Weaving outputs together...",
          progress: 0.7
        }
      );

      const synthesis = JSON.parse(response);
      
      await emitOrchestrationEvent(
        this.executionId,
        'synthesis_progress',
        {
          commentary: "✨ Finalizing synthesized response...",
          progress: 1.0
        }
      );

      return synthesis;
    } catch (error) {
      console.warn(`[Moderator] Synthesis error: ${error}`);
      
      // Fallback: simple concatenation
      const combinedOutput = stepOutputs
        .map(step => `**${step.roleId} Contribution:**\n${step.output}`)
        .join('\n\n');

      return {
        combinedOutput,
        methodology: 'Simple concatenation due to synthesis error',
        qualityScore: 0.6,
        conflictsResolved: [],
        improvements: []
      };
    }
  }

  // Generate entertaining commentary for user engagement
  generateLiveCommentary(
    eventType: OrchestrationEventType,
    context: any
  ): string {
    const commentaries: Record<OrchestrationEventType, string[]> = {
      orchestration_started: [
        "🎬 Alright team, we've got an interesting challenge ahead!",
        "🚀 Let's break this down and see who's best suited for each part.",
        "🎯 Time to coordinate our AI specialists for optimal results."
      ],
      task_decomposed: [
        "📋 Task analysis complete! I've identified the perfect team composition.",
        "🎪 Perfect breakdown! Each specialist will handle their area of expertise.",
        "⚡ Task decomposition successful! Ready to assign specialists."
      ],
      step_assigned: [
        `📋 Assigning ${context.roleId} specialist to handle this part.`,
        `🎪 Our ${context.roleId} expert is stepping up to the plate!`,
        `⚡ Perfect match - ${context.roleId} is exactly what we need here.`
      ],
      step_started: [
        `🎬 ${context.roleId} is diving deep into this challenge...`,
        `⚡ Watch ${context.roleId} work their specialized magic!`,
        `🎯 ${context.roleId} is laser-focused on delivering excellence.`
      ],
      step_progress: [
        `🔥 ${context.roleId} is making great progress...`,
        `⚙️ The gears are turning smoothly in ${context.roleId}'s domain!`,
        `🌟 ${context.roleId} is crafting something special...`
      ],
      step_streaming: [
        `📡 ${context.roleId} is streaming their thoughts in real-time...`,
        `⚡ Live updates from ${context.roleId} - watch the magic happen!`,
        `🌊 ${context.roleId} is flowing with brilliant insights...`
      ],
      step_completed: [
        `✅ Excellent work from ${context.roleId}! Moving to the next phase.`,
        `🎉 ${context.roleId} delivered exactly what we needed. Handoff time!`,
        `💫 Beautiful execution by ${context.roleId}. The team is flowing perfectly.`
      ],
      step_failed: [
        `⚠️ ${context.roleId} hit a snag, but we're adapting quickly!`,
        `🔄 Minor setback with ${context.roleId} - implementing recovery strategy.`,
        `🛠️ ${context.roleId} needs a different approach. Adjusting tactics...`
      ],
      synthesis_started: [
        "🧩 Time for the grand finale! I'm combining all these brilliant contributions...",
        "🎭 Watch as I orchestrate these individual masterpieces into one cohesive symphony!",
        "🌟 The magic happens now - weaving together all our specialists' expertise!"
      ],
      synthesis_progress: [
        "🔄 Synthesis in progress - combining specialist outputs...",
        "🎨 Weaving together the brilliant contributions...",
        "⚙️ Processing and harmonizing all the expert insights..."
      ],
      synthesis_streaming: [
        "📡 Streaming the synthesis process live...",
        "🌊 Watch the final result take shape in real-time...",
        "⚡ Live synthesis - see how all pieces come together..."
      ],
      synthesis_complete: [
        "🎊 Synthesis complete! All specialist outputs have been perfectly combined.",
        "✨ The final masterpiece is ready! What an incredible team effort.",
        "🏆 Synthesis successful! The AI team has delivered excellence."
      ],
      orchestration_completed: [
        "🎉 What a performance! Our AI team delivered something truly remarkable.",
        "✨ Mission accomplished! Each specialist played their part perfectly.",
        "🏆 Outstanding collaboration - this is what AI teamwork looks like!"
      ],
      orchestration_failed: [
        "⚠️ The orchestration encountered issues, but we're learning from this.",
        "🔄 Orchestration failed - analyzing what went wrong for next time.",
        "🛠️ Technical difficulties occurred - implementing improvements."
      ],
      moderator_commentary: [
        "🎙️ Moderator providing guidance and coordination...",
        "📋 Quality check and process optimization in progress...",
        "🎯 Ensuring optimal team coordination and output quality..."
      ],
      specialist_message: [
        `💬 ${context.roleId} is sharing insights with the team...`,
        `🗣️ ${context.roleId} has something important to communicate...`,
        `📢 ${context.roleId} is contributing to the team discussion...`
      ],
      moderator_assignment: [
        `🎯 Moderator assigning ${context.roleId} to the next task...`,
        `📋 Task delegation: ${context.roleId} is now taking the lead...`,
        `⚡ ${context.roleId} has been selected for this specialized work...`
      ],
      specialist_acknowledgment: [
        `✅ ${context.roleId} acknowledges the assignment and is ready to proceed.`,
        `👍 ${context.roleId} confirms understanding and begins work.`,
        `🎯 ${context.roleId} is locked and loaded for this task.`
      ],
      handoff_message: [
        `🤝 ${context.roleId} is handing off to the next specialist...`,
        `📤 ${context.roleId} has completed their part - passing the baton...`,
        `✨ ${context.roleId} finished beautifully - next specialist incoming...`
      ],
      clarification_request: [
        `❓ ${context.roleId} needs clarification to deliver the best results...`,
        `🤔 ${context.roleId} is asking for more details to optimize their output...`,
        `💭 ${context.roleId} wants to ensure they understand the requirements perfectly...`
      ],
      clarification_response: [
        `💡 Clarification provided - ${context.roleId} now has what they need!`,
        `✅ Question answered - ${context.roleId} can proceed with confidence.`,
        `🎯 All clear! ${context.roleId} has the details needed for success.`
      ]
    };

    const options = commentaries[eventType] || ["🤖 Processing..."];
    return options[Math.floor(Math.random() * options.length)];
  }

  // Make API call to moderator model
  private async callModerator(prompt: string): Promise<string> {
    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/openai/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.classificationApiKey}`,
      },
      body: JSON.stringify({
        model: 'gemini-2.0-flash-lite',
        messages: [
          { 
            role: 'system', 
            content: 'You are an expert AI orchestration moderator. You coordinate multiple AI specialists, validate their work, resolve conflicts, and synthesize their outputs into cohesive results. Always respond in the requested JSON format.' 
          },
          { role: 'user', content: prompt }
        ],
        temperature: 0.2,
        max_tokens: 2000,
        response_format: { type: "json_object" }
      }),
    });

    if (!response.ok) {
      throw new Error(`Moderator API error: ${response.status}`);
    }

    const result = await response.json();
    const content = result.choices?.[0]?.message?.content;
    
    if (!content) {
      throw new Error('Empty moderator response');
    }

    return content;
  }

  // Determine if steps can run in parallel
  async analyzeParallelizationOpportunities(
    steps: Array<{
      stepNumber: number;
      roleId: string;
      prompt: string;
      dependencies: number[];
    }>
  ): Promise<{
    parallelGroups: number[][];
    reasoning: string;
    estimatedSpeedup: number;
  }> {
    const analysisPrompt = `Analyze these orchestration steps for parallelization opportunities:

Steps:
${steps.map(step => 
  `${step.stepNumber}. ${step.roleId}: "${step.prompt}" (depends on: ${step.dependencies.join(', ') || 'none'})`
).join('\n')}

Determine:
1. Which steps can run in parallel
2. Optimal grouping strategy
3. Expected performance improvement

Respond in JSON format:
{
  "parallelGroups": [[1], [2, 3], [4]],
  "reasoning": "explanation of grouping strategy",
  "estimatedSpeedup": 1.8
}`;

    try {
      const response = await this.callModerator(analysisPrompt);
      return JSON.parse(response);
    } catch (error) {
      console.warn(`[Moderator] Parallelization analysis error: ${error}`);
      
      // Fallback: sequential execution
      return {
        parallelGroups: steps.map(step => [step.stepNumber]),
        reasoning: 'Sequential execution due to analysis error',
        estimatedSpeedup: 1.0
      };
    }
  }
}
